:root {
    --primary-color: #30abe8;
    --primary-light: #1E546A;
    --secondary-color: #ffc619;
    --text-color: #95D7E3;
    --background-color: #05202e;
    --card-background: #102735;
    --border-color: #30abe8;
    --hover-color: #2a7dc9;
    --active-color: #47ebeb;
    --card-radius: 0.4vw;
    --base-font-size: 0.8vw;
    --small-font-size: 0.7vw;
    --tiny-font-size: 0.6vw;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    font-size: var(--base-font-size);
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.4;
    width: 100vw;
    height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 98vw;
    margin: 4vh auto;
    padding: 0 0.7vw;
    width: 100vw;
    height: 96vh;
}

header {
    text-align: center;
    margin-bottom: 1vh;
}

header h1 {
    font-size: 1.4vw;
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    display: inline-block;
    padding-bottom: 0.3vh;
}

header h1:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 4vw;
    height: 0.15vh;
    background: var(--primary-color);
    border-radius: 0.15vh;
}

.card {
    background: var(--card-background);
    border-radius: var(--card-radius);
    box-shadow: 0 0.15vh 0.4vh rgba(0, 0, 0, 0.3);
    padding: 0.8vh 1vw;
    margin-bottom: 0.8vh;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 0.05vw solid rgba(48, 171, 232, 0.2);
    animation: fadeIn 0.3s ease-out;
}

.card:hover {
    transform: translateY(-0.2vh);
    box-shadow: 0 0.3vh 0.7vh rgba(0, 0, 0, 0.35);
    border-color: rgba(48, 171, 232, 0.4);
}

h2 {
    color: var(--primary-color);
    font-size: var(--base-font-size);
    margin-bottom: 0.7vh;
    padding-bottom: 0.3vh;
    border-bottom: 0.05vw solid rgba(48, 171, 232, 0.6);
}

.option-group {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.7vw;
    margin-bottom: 0.5vh;
    background-color: rgba(5, 32, 46, 0.6);
    border-radius: 0.3vw;
    padding: 0.5vh;
}

.option-group.compact {
    gap: 0.4vw;
    margin-bottom: 0.3vh;
}

.option {
    display: flex;
    align-items: center;
    gap: 0.3vw;
}

.option input[type="radio"] {
    appearance: none;
    width: 0.8vw;
    height: 0.8vw;
    border: 0.05vw solid var(--border-color);
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;
}

.option input[type="radio"]:checked {
    border-color: var(--primary-color);
}

.option input[type="radio"]:checked::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0.4vw;
    height: 0.4vw;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.option label {
    font-size: var(--small-font-size);
    cursor: pointer;
    color: var(--text-color);
}

.upload-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 0.05vw solid var(--primary-color);
    border-radius: 0.2vw;
    padding: 0.3vh 0.4vw;
    font-size: 0.6vw;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.3vw;
    transition: all 0.3s;
    margin-left: auto;
    white-space: nowrap;
    min-width: 8vw;
    justify-content: center;
}

.upload-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
    border-color: var(--hover-color);
    transform: translateY(-0.15vh);
    box-shadow: 0 0.15vh 0.3vh rgba(0, 0, 0, 0.2);
}

.upload-btn:active {
    transform: translateY(0);
    background-color: rgba(48, 171, 232, 0.5);
    box-shadow: 0 0.07vh 0.15vh rgba(0, 0, 0, 0.15);
}

/* 加载已有结果按钮样式 */
.load-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 0.05vw solid var(--primary-color);
    border-radius: 0.2vw;
    padding: 0.3vh 0.7vw;
    font-size: var(--small-font-size);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.3vw;
    transition: all 0.3s;
    white-space: nowrap;
    justify-content: center;
    margin: 0 0.3vw;
}

.load-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
    border-color: var(--hover-color);
    color: var(--active-color);
}

.sub-options {
    margin-top: 0.5vh;
    background-color: rgba(5, 32, 46, 0.6);
    border-radius: 0.3vw;
    padding: 0.5vh;
}

/* 添加车辆类型和组织时段的水平布局容器 */
.vehicle-time-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    gap: 0.7vw;
}

.vehicle-time-container .sub-option {
    flex: 1;
    margin-bottom: 0.3vh;
}

.vehicle-time-container .select-wrapper {
    flex: 1;
}

.vehicle-time-container select {
    min-width: 6.7vw;
    width: 100%;
}

.sub-option {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 0.5vh;
    gap: 0.4vw;
}

.sub-option.indent {
    margin-left: 1vw;
}

.bullet {
    font-size: 1.1vw;
    color: var(--primary-color);
    line-height: 1;
}

.select-wrapper {
    position: relative;
    margin-right: 0.5vw;
}

.select-wrapper:after {
    content: '▼';
    font-size: 0.5vw;
    color: var(--primary-color);
    position: absolute;
    right: 0.4vw;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

select {
    appearance: none;
    padding: 0.3vh 1.3vw 0.3vh 0.4vw;
    border-radius: 0.2vw;
    border: 0.05vw solid var(--primary-color);
    background: var(--primary-light);
    color: var(--text-color);
    font-size: var(--small-font-size);
    cursor: pointer;
    outline: none;
    min-width: 8vw;
    box-shadow: 0 0.07vh 0.2vh rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

select:hover {
    border-color: var(--hover-color);
    box-shadow: 0 0.1vh 0.3vh rgba(0, 0, 0, 0.25);
}

select:focus {
    border-color: var(--active-color);
    box-shadow: 0 0 0 0.1vw rgba(71, 235, 235, 0.25);
}

select option {
    background-color: var(--background-color);
}

select option:hover {
    background: var(--secondary-color);
}

select:disabled,
input:disabled {
    background-color: rgba(5, 32, 46, 0.8);
    border-color: #4a5a65;
    color: #8b949e;
    cursor: not-allowed;
}

.option-group:has(input[type="radio"]:disabled) {
    opacity: 0.7;
}

/* 改进复选框样式 */
input[type="checkbox"] {
    appearance: none;
    width: 0.6vw;
    height: 0.6vw;
    border: 0.05vw solid var(--border-color);
    border-radius: 0.15vw;
    outline: none;
    cursor: pointer;
    position: relative;
    margin-right: 0.3vw;
    transition: all 0.2s ease;
}

input[type="checkbox"]:hover {
    border-color: var(--hover-color);
    box-shadow: 0 0 0 0.05vw rgba(48, 171, 232, 0.3);
}

input[type="checkbox"]:checked {
    background-color: transparent;
    border-color: var(--active-color);
}

input[type="checkbox"]:checked::before {
    content: '✓';
    position: absolute;
    color: var(--active-color);
    font-size: 0.5vw;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.note {
    color: #8b949e;
    font-size: 0.55vw;
    font-style: italic;
}

.run-btn {
    display: block;
    margin: 1vh auto;
    background-color: transparent;
    color: var(--secondary-color);
    border: 0.05vw solid var(--secondary-color);
    border-radius: 0.2vw;
    padding: 0.5vh 1.3vw;
    font-size: 0.85vw;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 0.15vh 0.3vh rgba(0, 0, 0, 0.2);
}

.run-btn:hover {
    background-color: rgba(255, 198, 25, 0.2);
    transform: translateY(-0.15vh);
    box-shadow: 0 0.2vh 0.4vh rgba(0, 0, 0, 0.25);
}

.run-btn:active {
    background-color: rgba(255, 198, 25, 0.3);
    transform: translateY(0.07vh);
    box-shadow: 0 0.07vh 0.15vh rgba(0, 0, 0, 0.15);
}

/* 添加结果卡片样式 */
.result-card {
    background-color: rgba(25, 35, 50, 0.8);
    border-radius: var(--card-radius);
    padding: 0.5vh;
    margin-bottom: 0.4vh;
    border: 0.05vw solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 0.2vh 0.7vh rgba(0, 0, 0, 0.25);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    color: var(--text-color);
    flex-shrink: 0;
    box-sizing: border-box;
    max-width: 100%;
}

.result-card:hover {
    transform: translateY(-0.15vh);
    box-shadow: 0 0.2vh 0.5vh rgba(0, 0, 0, 0.25);
    border-color: rgba(48, 171, 232, 0.6);
}

.result-header {
    margin-bottom: 0.8vh;
    padding-bottom: 0.5vh;
    border-bottom: 0.05vw solid rgba(48, 171, 232, 0.6);
}

.result-header h2 {
    color: var(--active-color);
    font-size: var(--base-font-size);
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5vw;
    margin-bottom: 0.8vh;
}

.metric-item {
    text-align: center;
    padding: 0.4vh;
    background-color: var(--primary-light);
    border-radius: 0.2vw;
    border: 0.05vw solid rgba(48, 171, 232, 0.3);
}

.metric-value {
    font-size: 0.6vw;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 0.1vh;
}

.metric-label {
    font-size: 0.45vw;
    color: var(--text-color);
}

/* 路段分析结果标题样式 */
.metrics-subtitle {
    font-size: 0.6vw;
    color: #ffffff;
    margin-bottom: 0.4vh;
    font-weight: bold;
}

/* 优化图表容器样式 */
.chart-container {
    height: 24vh; /* 进一步增加图表高度，从18vh增加到24vh */
    min-height: 20vh; /* 增加最小高度，从16vh */
    max-height: 36vh; /* 增加最大高度，从30vh */
    width: 100%;
    margin-top: 0.4vh;
    background-color: rgba(5, 32, 46, 0.6);
    border-radius: 0.3vw;
    padding: 0.5vh;
    position: relative;
    border: 0.05vw solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    box-shadow: inset 0 0.07vh 0.2vh rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
}





.back-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 0.05vw solid var(--primary-color);
    border-radius: 0.2vw;
    padding: 0.4vh 0.8vw;
    font-size: 0.7vw;
    cursor: pointer;
    margin-top: 0.8vh;
    align-self: flex-end;
    transition: all 0.3s;
}

.back-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
    color: var(--active-color);
    border-color: var(--active-color);
}

/* 组织方案网格布局 */
.org-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 0.4vw;
}

.org-grid .sub-option {
    margin-bottom: 0.25vh;
    font-size: 0.9em;
}

/* 调整选择框大小 */
.org-grid select {
    min-width: 5vw;
    padding: 0.15vh 0.9vw 0.15vh 0.25vw;
}

/* 调整信号配时部分 */
.org-grid .option-group.compact {
    padding: 0.25vh;
    margin-bottom: 0.15vh;
}

/* 调整信号配时的布局，使其占据两列 */
.org-grid .sub-option:nth-child(4) {
    grid-column: span 2;
}

/* 缩小复选框间距 */
.checkbox-wrapper {
    margin-left: 0.25vw;
}

/* 禁用状态样式 */
.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.disabled select,
.disabled input,
.disabled button {
    pointer-events: none;
}

/* 美化滚动条样式 */
::-webkit-scrollbar {
    width: 0.4vw;
    height: 0.4vw;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 0.2vw;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0.2vw;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 改进表单元素样式 */
.form-group {
    margin-bottom: 0.8vh;
}

.form-group label {
    display: block;
    margin-bottom: 0.25vh;
    font-weight: bold;
    color: var(--text-color);
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 0.4vh 0.6vw;
    border: 0.05vw solid var(--primary-color);
    border-radius: 0.2vw;
    background-color: rgba(5, 32, 46, 0.6);
    color: var(--text-color);
    font-size: 0.6vw;
    resize: vertical;
}

.form-group input:focus, .form-group textarea:focus {
    outline: none;
    border-color: var(--active-color);
    box-shadow: 0 0 0 0.1vw rgba(71, 235, 235, 0.2);
}

.config-preview {
    background-color: rgba(30, 84, 106, 0.6);
    border-radius: 0.3vw;
    padding: 0.8vh;
    margin-bottom: 1vh;
}

.config-preview h4 {
    margin: 0 0 0.5vh 0;
    color: var(--text-color);
    font-size: 0.7vw;
}

.config-summary {
    font-size: 0.6vw;
    color: #b0c4d1;
    line-height: 1.4;
}

.config-summary div {
    margin-bottom: 0.25vh;
}

/* 模态框操作按钮 */
.modal-actions {
    display: flex;
    gap: 0.5vw;
    justify-content: flex-end;
}

.confirm-btn, .cancel-btn {
    padding: 0.4vh 1vw;
    border: none;
    border-radius: 0.2vw;
    font-size: 0.7vw;
    cursor: pointer;
    transition: all 0.3s;
}

.confirm-btn {
    background-color: transparent;
    color: var(--secondary-color);
    border: 0.05vw solid var(--secondary-color);
}

.confirm-btn:hover {
    background-color: rgba(255, 198, 25, 0.2);
}

.cancel-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 0.05vw solid var(--primary-color);
}

.cancel-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
}

/* 组织方案布局样式 */
.org-options-layout {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5vw;
    margin-top: 0.5vh;
}

.org-column {
    flex: 1 1 14.7vw;
    display: flex;
    flex-direction: column;
    gap: 0.4vh;
}

.org-group {
    padding: 0.5vh;
    background-color: rgba(255,255,255,0.05);
    border-radius: var(--card-radius);
    display: flex;
    flex-direction: column;
    gap: 0.3vh;
}

.signal-group {
    margin-top: 0.3vh;
}

.signal-options {
    display: flex;
    align-items: center;
    gap: 0.5vw;
    flex-wrap: nowrap;
}

.signal-options .upload-btn {
    margin-top: 0;
}

/* 选择按钮样式 */
.select-btn {
    background-color: #6c757d;
    color: white;
    border: 0.1vw solid #5a6268;
    border-radius: 0.3vw;
    padding: 0.5vh 1.1vw;
    font-size: 0.9vw;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.4vw;
    width: 100%;
    justify-content: center;
    transition: all 0.3s;
}

.select-btn:hover {
    background-color: #5a6268;
    border-color: #4a545a;
}

.select-btn:disabled {
    background-color: #495057;
    color: #adb5bd;
    cursor: not-allowed;
    opacity: 0.6;
}

/* 操作按钮容器样式 */
.action-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.7vw;
    margin: 1vh 0;
    flex-wrap: wrap;
}

/* 加载和空状态消息 */
.loading-message, .empty-message {
    text-align: center;
    color: #8b949e;
    font-style: italic;
    padding: 2vh 1vw;
}

/* 响应式设计 */
@media (max-width: 767px) {
    main {
        flex-direction: column !important;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 使用网格布局使页面更紧凑 */
@media (min-width: 768px) {
    main {
        display: flex;
        flex-wrap: wrap;
    }
    #organizationSection {
        height: auto;
    }
    .run-btn {
        width: 10vw;
        font-size: 0.7vw;
    }
}

@media (max-width: 767px) {
    .container {
        padding: 0 0.5vw;
        margin: 0.5vh auto;
    }
    .option-group {
        flex-direction: column;
        align-items: flex-start;
    }
    .upload-btn {
        margin-left: 0;
        margin-top: 0.25vh;
        min-width: auto;
        width: 100%;
    }
    .sub-option {
        flex-direction: column;
        align-items: flex-start;
    }
    select {
        width: 100%;
    }
    .org-grid {
        grid-template-columns: 1fr;
    }
}

/* 历史方案相关样式 */
.history-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 0.05vw solid var(--primary-color);
    border-radius: 0.2vw;
    padding: 0.3vh 0.7vw;
    font-size: var(--small-font-size);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.3vw;
    transition: all 0.3s;
    white-space: nowrap;
    justify-content: center;
    margin: 0 0.3vw;
}

.history-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
    border-color: var(--hover-color);
    color: var(--active-color);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: rgba(5, 32, 46, 0.9);
    margin: 5% auto;
    padding: 0;
    border-radius: 0.4vw;
    width: 80%;
    max-width: 40vw;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 0.3vh 1.3vh rgba(0, 0, 0, 0.35), 0 0 0.05vw rgba(48, 171, 232, 0.6);
    border: 0.05vw solid rgba(48, 171, 232, 0.6);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.modal-header {
    background-color: rgba(5, 29, 46, 0.8);
    color: var(--text-color);
    padding: 0.8vh 1vw;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 0.05vw solid rgba(48, 171, 232, 0.6);
}

.modal-header h3 {
    margin: 0;
    font-size: 0.45vw;
    color: var(--primary-color);
}

.close {
    color: var(--text-color);
    font-size: 1.2vw;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: var(--active-color);
}

.modal-body {
    padding: 1vh;
    color: var(--text-color);
    max-height: 60vh;
    overflow-y: auto;
}

/* 历史方案控制按钮 */
.history-controls {
    display: flex;
    gap: 0.5vw;
    margin-bottom: 1vh;
    flex-wrap: wrap;
}

.save-btn, .refresh-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 0.05vw solid var(--primary-color);
    border-radius: 0.2vw;
    padding: 0.4vh 0.8vw;
    font-size: 0.6vw;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.3vw;
    transition: all 0.3s;
}

.save-btn:hover, .refresh-btn:hover {
    background-color: rgba(48, 171, 232, 0.3);
    color: var(--active-color);
    border-color: var(--active-color);
}

/* 历史方案列表 */
.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    background-color: rgba(30, 84, 106, 0.6);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s;
}

.history-item:hover {
    background-color: rgba(30, 84, 106, 0.8);
    border-left-color: var(--active-color);
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.history-item-title {
    font-weight: bold;
    font-size: 14px;
    color: var(--text-color);
}

.history-item-time {
    font-size: 11px;
    color: #8b949e;
}

.history-item-description {
    color: #b0c4d1;
    font-size: 12px;
    margin-bottom: 10px;
    line-height: 1.4;
}

.history-item-config {
    font-size: 11px;
    color: #8b949e;
    margin-bottom: 10px;
}

.history-item-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.load-scheme-btn, .delete-scheme-btn {
    padding: 4px 12px;
    font-size: 12px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s;
}

.load-scheme-btn {
    background-color: transparent;
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
}

.load-scheme-btn:hover {
    background-color: rgba(255, 198, 25, 0.2);
}

.delete-scheme-btn {
    background-color: transparent;
    color: #ed6f6f;
    border: 1px solid #ed6f6f;
}

.delete-scheme-btn:hover {
    background-color: rgba(237, 111, 111, 0.2);
}

/* 添加动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(71, 235, 235, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(71, 235, 235, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(71, 235, 235, 0);
    }
}



.run-btn:focus {
    animation: pulse 1.5s infinite;
}

/* 加载动画效果 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
    padding: 30px;
}

/* 加载中动画样式 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-left-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 767px) {
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .history-controls {
        flex-direction: column;
    }

    .history-item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .history-item-actions {
        justify-content: flex-start;
    }

    .modal-actions {
        flex-direction: column;
    }

    .confirm-btn, .cancel-btn {
        width: 100%;
    }
}

/* 视口单位响应式设计 - 针对极端屏幕比例调整 */
@media (max-aspect-ratio: 4/3) {
    /* 竖屏或接近正方形屏幕 */
    :root {
        --base-font-size: 1.2vw;
        --small-font-size: 1vw;
        --tiny-font-size: 0.9vw;
    }

    .chart-container {
        height: 26vh; /* 调整竖屏下的图表高度，从20vh */
        min-height: 22vh;
    }
    
    .edge-chart-container {
        height: 30vh; /* 调整竖屏下的路段图表高度，从24vh */
    }
}

@media (min-aspect-ratio: 21/9) {
    /* 超宽屏 */
    :root {
        --base-font-size: 0.6vw;
        --small-font-size: 0.5vw;
        --tiny-font-size: 0.4vw;
    }

    .chart-container {
        height: 22vh; /* 调整超宽屏下的图表高度，从16vh */
        min-height: 18vh;
    }
    
    .edge-chart-container {
        height: 26vh; /* 调整超宽屏下的路段图表高度，从20vh */
    }
}



/* 页面布局类 */
.container {
    position: relative;
    height: 100vh;
    width: 100vw;
}

main {
    position: relative;
    padding-top: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: row;
    gap: 0.7vw;
}

.left-panel {
    position: fixed;
    top: 5vh;
    left: 0.7vw;
    width: 20vw;
    height: 94vh;
    overflow-y: auto;
    z-index: 10;
    flex: 1;
    max-width: 20vw;
    margin-top: 4vh;
}

.right-panel {
    position: fixed;
    top: 4vh;
    left: 21.5vw;
    right: 0.7vw;
    height: 95vh;
    overflow-y: auto;
    z-index: 10;
    flex: 3;
    display: flex;
    flex-direction: row;
    margin-top: 2.7vh;
}

.map-section {
    flex: 7;
    display: flex;
    flex-direction: column;
    margin-right: 1vw;
}

.charts-section {
    flex: 3;
    display: flex;
    flex-direction: column;
}

.charts-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.7vh;
    padding-left: 0.3vw;
}

.charts-header h3 {
    margin: 0;
    font-size: 1.1vw;
    color: #ffffff;
}

.charts-container {
    display: flex;
    flex-direction: column;
    gap: 0.7vh;
    max-height: 85vh;
    overflow-y: auto;
    padding: 0 0.7vw 0 0.3vw;
}

/* 历史方案样式 */
.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5vh;
    border-bottom: 0.05vw solid #e0e0e0;
    background-color: rgba(255, 255, 255, 0.05);
    margin-bottom: 0.4vh;
    border-radius: 0.3vw;
}

.history-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.history-info {
    flex: 3;
    padding-right: 0.3vw;
}

.history-name {
    font-weight: bold;
    font-size: 0.9vw;
    color: #ffffff;
}

.history-date {
    font-size: 0.8vw;
    color: #aaaaaa;
    margin-top: 0.2vh;
}

.history-actions {
    display: flex;
    gap: 0.3vw;
}

.history-actions button {
    padding: 0.3vh 0.5vw;
    border: none;
    border-radius: 0.2vw;
    cursor: pointer;
    font-size: 0.85vw;
    transition: background-color 0.2s;
}

/* 按钮样式 */
.view-btn {
    background-color: #2196F3;
    color: white;
}

.view-btn:hover {
    background-color: #0b7dda;
}

.compare-btn {
    background-color: #4CAF50;
    color: white;
}

.compare-btn:hover {
    background-color: #46a049;
}

.delete-btn {
    background-color: #f44336;
    color: white;
}

.delete-btn:hover {
    background-color: #da190b;
}

.save-btn {
    background-color: #4CAF50;
    color: white;
}

.refresh-btn {
    background-color: #2196F3;
    color: white;
}

/* 配置对比样式 */
.config-comparison {
    display: flex;
    gap: 0.7vw;
}

.current-config, .compare-config {
    flex: 1;
    padding: 0.5vh;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.3vw;
}

.current-config h3, .compare-config h3 {
    margin-top: 0;
    padding-bottom: 0.3vh;
    border-bottom: 0.05vw solid rgba(255, 255, 255, 0.2);
    font-size: 0.7vw;
}

.compare-config {
    border-left: 0.15vw solid rgba(255, 99, 132, 0.8);
}

.loading-message, .empty-message {
    padding: 0.7vh;
    text-align: center;
    color: #aaaaaa;
}

.history-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1vh;
}

.save-btn, .refresh-btn {
    padding: 0.4vh 0.8vw;
    border: none;
    border-radius: 0.3vw;
    cursor: pointer;
    font-size: 0.9vw;
    display: flex;
    align-items: center;
    gap: 0.3vw;
}

/* 方案对比按钮样式 */
.result-actions {
    position: absolute;
    top: -0.5vh;
    right: 0.7vw;
}

.compare-action-btn {
    background-color: #4CAF50;
    color: white;
    padding: 0.3vh 0.7vw;
    border: none;
    border-radius: 0.3vw;
    cursor: pointer;
    font-size: 0.9vw;
    display: flex;
    align-items: center;
    gap: 0.3vw;
}

.compare-action-btn:hover {
    background-color: #46a049;
}

.result-header {
    position: relative;
    padding-bottom: 0.3vh;
    margin-bottom: 0.3vh;
}

/* 对比结果显示样式 */
.comparison-header {
    margin-top: 1vh;
    padding: 0.7vh;
    background-color: rgba(76, 175, 80, 0.2);
    border-radius: 0.3vw;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comparison-title {
    font-size: 1.1vw;
    font-weight: bold;
    color: #ffffff;
}

.close-comparison {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.2vw;
    cursor: pointer;
}

/* 路段分析样式 */
.edge-analysis-details {
    background-color: #4a5a65;
    border-radius: 0.5vw;
    padding: 0.7vh;
}

/* 仿真信息配置样式 */
.simulation-info-config {
    flex-grow: 1;
    overflow-y: auto;
    height: 22vh; /* 调整高度以适应地图尺寸，从25vh调整为22vh */
    margin-bottom: 0;
}

.simulation-title {
    font-size: 0.9vw;
    color: #8acdff;
}

.simulation-info {
    margin-bottom: 0.3vh;
}

.sim-time {
    margin-left: 1vw;
}

.comparison-schemes-info {
    margin-bottom: 0.3vh;
    color: #8defff;
    font-size: 0.8vw;
}

.comparison-schemes-summary {
    margin-bottom: 0.3vh;
    font-size: 0.7vw;
}

.current-config {
    font-size: 0.7vw;
}

.current-config h3 {
    font-size: 0.85vw;
    margin: 0.3vh 0;
}

/* 图表标题样式 */
.chart-title {
    font-size: 1.2vw; /* 增加字体大小，从原来的0.9vw */
    color: #8acdff;
}

.result-header h2 {
    color: var(--active-color);
    font-size: 1.2vw; /* 增加字体大小 */
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* 增加指标子标题字体大小 */
.metrics-subtitle {
    font-size: 1.1vw; /* 增加字体大小，从原来的0.8vw */
    color: #ffffff;
    margin-bottom: 0.5vh;
    font-weight: bold;
}

/* 仿真概况标题 */
.simulation-title {
    font-size: 1.2vw; /* 增加字体大小，从原来的0.9vw */
    color: #8acdff;
}

/* 地图容器样式 */
.map-container {
    margin-bottom: 1vh;
}

/* 状态和选择器样式 */
.selection-status {
    margin-left: 0.5vw;
    font-size: 0.8vw;
    color: #28a745;
    display: none;
}

.analysis-status {
    display: none;
    color: #4caf50;
    margin-left: 0.3vw;
    font-size: 0.6vw;
}

.analysis-details {
    display: none;
    margin-left: 1.3vw;
    margin-top: 0.7vh;
}

.embedded-selector-view {
    display: none;
}

/* 路段分析样式 */
.edge-analysis-section {
    display: none;
}

.edge-analysis-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.7vw;
    justify-content: space-between;
}

.edge-analysis-column {
    flex: 1;
    min-width: 48%;
}

.metrics-subtitle {
    font-size: 0.8vw;
    color: #ffffff;
    margin-bottom: 0.5vh;
    font-weight: bold;
}

.edge-metrics-grid {
    grid-template-columns: repeat(auto-fill, minmax(3.3vw, 1fr));
    gap: 0.3vw;
    margin-bottom: 0.5vh;
}

.edge-metric-item {
    padding: 0.3vh;
}

/* 调整指标项的字体大小 */
.metric-value {
    font-size: 1vw; /* 增加字体大小，从原来的0.6vw */
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 0.1vh;
}

.metric-label {
    font-size: 0.75vw; /* 增加字体大小，从原来的0.45vw */
    color: var(--text-color);
}

/* 调整路段分析区域指标字体大小 */
.edge-metric-item .metric-value {
    font-size: 1vw; /* 增加字体大小，从原来的0.7vw */
}

.edge-metric-item .metric-label {
    font-size: 0.75vw; /* 增加字体大小，从原来的0.5vw */
}

/* 修改路段图表容器高度 */
.edge-chart-container {
    height: 28vh; /* 进一步增加路段图表高度，从22vh增加到28vh */
}

/* 添加标签组件样式 */
.tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin-right: 5px;
    margin-bottom: 5px;
    background-color: rgba(48, 171, 232, 0.2);
    color: var(--primary-color);
    border: 1px solid rgba(48, 171, 232, 0.3);
}

.tag.success {
    background-color: rgba(85, 209, 135, 0.2);
    color: #55d187;
    border-color: rgba(85, 209, 135, 0.3);
}

.tag.warning {
    background-color: rgba(255, 198, 25, 0.2);
    color: var(--secondary-color);
    border-color: rgba(255, 198, 25, 0.3);
}

.tag.error {
    background-color: rgba(237, 111, 111, 0.2);
    color: #ed6f6f;
    border-color: rgba(237, 111, 111, 0.3);
}

.tag .close {
    margin-left: 5px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1;
}

/* 添加提示框样式 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: rgba(5, 32, 46, 0.9);
    color: var(--text-color);
    text-align: center;
    border-radius: 4px;
    padding: 8px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 11px;
    border: 1px solid rgba(48, 171, 232, 0.4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(5, 32, 46, 0.9) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* 添加进度条样式 */
.progress-bar {
    width: 100%;
    height: 6px;
    background-color: rgba(5, 32, 46, 0.6);
    border-radius: 3px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar .progress {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-bar .progress.success {
    background-color: #55d187;
}

.progress-bar .progress.warning {
    background-color: var(--secondary-color);
}

.progress-bar .progress.error {
    background-color: #ed6f6f;
}

/* 添加状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-indicator.online {
    background-color: #55d187;
    box-shadow: 0 0 5px #55d187;
}

.status-indicator.offline {
    background-color: #8b949e;
}

.status-indicator.warning {
    background-color: var(--secondary-color);
    box-shadow: 0 0 5px var(--secondary-color);
}

.status-indicator.error {
    background-color: #ed6f6f;
    box-shadow: 0 0 5px #ed6f6f;
}

/* 添加卡片组和网格布局 */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.card-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 15px 0;
}

/* 添加徽章样式 */
.badge {
    position: relative;
    display: inline-block;
}

.badge[data-count]:after {
    content: attr(data-count);
    position: absolute;
    top: -8px;
    right: -8px;
    min-width: 16px;
    height: 16px;
    line-height: 16px;
    padding: 0 4px;
    font-size: 9px;
    font-weight: bold;
    color: #fff;
    text-align: center;
    background: var(--primary-color);
    border-radius: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.badge.danger[data-count]:after {
    background-color: #ed6f6f;
}

.badge.warning[data-count]:after {
    background-color: var(--secondary-color);
}

.badge.success[data-count]:after {
    background-color: #55d187;
}

/* 固定的选择器/地图容器样式 */
.fixed-selector-container {
    width: 100%;
    height: 72vh; /* 调整地图容器高度，从65vh增加到72vh */
    min-height: 36vh; /* 增加最小高度 */
    max-height: 75vh; /* 增加最大高度 */
    border: 0.05vw solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5vw;
    overflow: hidden;
    position: relative;
    background-color: rgba(30, 30, 30, 0.8);
    box-shadow: 0 0.3vh 1vh rgba(0, 0, 0, 0.35);
    transition: box-shadow 0.3s ease;
}

.fixed-selector-container:hover {
    box-shadow: 0 0.4vh 1.2vh rgba(0, 0, 0, 0.45);
}

/* 美化默认地图视图 */
.default-map-view {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: linear-gradient(to bottom, #1a1a1a 0%, #2a2a2a 100%);
}

.map-placeholder {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
}

.map-placeholder .bi-map {
    font-size: 4.3vw;
    margin-bottom: 1vh;
    color: rgba(54, 162, 235, 0.7);
}

.map-placeholder p {
    font-size: 1.1vw;
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
}

.map-placeholder small {
    font-size: 0.8vw;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 0.5vh;
    display: block;
}

/* 地图加载状态样式 */
.map-loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #95D7E3;
}

.map-loading .bi-map {
    font-size: 3.2vw;
    margin-bottom: 0.7vh;
    color: rgba(54, 162, 235, 0.7);
}

.map-loading p {
    font-size: 1.1vw;
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
}

.map-view {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #1a1a1a;
}

#map-canvas {
    width: 100%;
    height: 100%;
    cursor: move;
}

/* 调整嵌入式选择器容器样式 */
.network-selector-embedded {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}

.network-selector-embedded .network-selector-modal {
    border-radius: 0;
    box-shadow: none;
}

/* 嵌入式选择器视图样式 - 确保充分利用容器空间 */
#embedded-selector-view {
    width: 100%;
    height: 100%;
    position: relative;
}